<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Generator Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
        }
        .section h2 {
            margin-top: 0;
            color: #555;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .template-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Template Generator Test Page</h1>
        
        <div class="section">
            <h2>📋 Instructions</h2>
            <ol>
                <li>Make sure the backend server is running on <code>http://localhost:3000</code></li>
                <li>Load the Chrome Extension from the <code>chrome-extension</code> folder</li>
                <li>Open the extension popup and test generating templates</li>
                <li>Use this page to test the template injection functionality</li>
            </ol>
        </div>

        <div class="section">
            <h2>🎯 Template Injection Test</h2>
            <p>This section simulates the app.localvanihq.net environment for testing.</p>
            
            <button onclick="registerHandler()">Register Template Handler</button>
            <button onclick="clearLogs()">Clear Logs</button>
            
            <div class="status info">
                <strong>Status:</strong> <span id="handlerStatus">No handler registered</span>
            </div>
            
            <div id="templateDisplay" class="template-display" style="display: none;">
                <h3>📄 Last Received Template:</h3>
                <pre id="templateContent"></pre>
            </div>
        </div>

        <div class="section">
            <h2>📝 Event Log</h2>
            <div id="eventLog" class="log">Waiting for events...</div>
        </div>

        <div class="section">
            <h2>🔧 Manual Testing</h2>
            <p>You can manually trigger template insertion for testing:</p>
            <button onclick="simulateTemplateInsertion()">Simulate Template Insertion</button>
        </div>
    </div>

    <script>
        let eventLog = document.getElementById('eventLog');
        let handlerStatus = document.getElementById('handlerStatus');
        let templateDisplay = document.getElementById('templateDisplay');
        let templateContent = document.getElementById('templateContent');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            eventLog.textContent += `[${timestamp}] ${message}\n`;
            eventLog.scrollTop = eventLog.scrollHeight;
        }

        function clearLogs() {
            eventLog.textContent = 'Logs cleared...\n';
        }

        function registerHandler() {
            // Register the template handler that the content script expects
            window.app = window.app || {};
            window.app.insertTemplate = function(templateData) {
                log('✅ Template received via window.app.insertTemplate()');
                log('Template data: ' + JSON.stringify(templateData, null, 2));
                
                // Display the template
                templateContent.textContent = JSON.stringify(templateData, null, 2);
                templateDisplay.style.display = 'block';
                
                return true;
            };
            
            handlerStatus.textContent = 'Handler registered successfully';
            handlerStatus.parentElement.className = 'status success';
            log('🔧 Template handler registered');
        }

        function simulateTemplateInsertion() {
            const mockTemplate = {
                background: "#FDBE33",
                elements: [
                    { type: "text", text: "Test Template", x: 100, y: 50, fontSize: 24 },
                    { type: "image", theme: "test", x: 200, y: 150 }
                ]
            };

            window.postMessage({
                type: 'INSERT_TEMPLATE',
                payload: mockTemplate
            }, '*');

            log('🧪 Simulated template insertion message sent');
        }

        // Listen for postMessage events (like the content script does)
        window.addEventListener('message', function(event) {
            log('📨 Received postMessage: ' + event.data.type);
            
            if (event.data && event.data.type === 'INSERT_TEMPLATE') {
                log('🎯 Template insertion message detected');
                
                if (window.app && window.app.insertTemplate) {
                    window.app.insertTemplate(event.data.payload);
                } else {
                    log('⚠️  No template handler registered');
                }
            }
        });

        // Listen for custom events
        document.addEventListener('templateInsert', function(event) {
            log('📡 Received custom event: templateInsert');
            log('Event detail: ' + JSON.stringify(event.detail, null, 2));
        });

        // Initialize
        log('🚀 Test page loaded and ready');
        log('💡 Click "Register Template Handler" to enable template reception');
    </script>
</body>
</html>
