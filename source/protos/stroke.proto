syntax="proto2";

// Stroke Properties

package com.zoho.shapes;

option java_package = "com.zoho.shapes";
option java_outer_classname = "StrokeProtos";

import "fill.proto";
import "marker.proto";
import "fields.proto";
import "elementcategory.proto";

message Stroke {
	
	optional Show.StrokeField.StrokeType type = 1; // stroke type

	optional float width = 2; // stroke width , indicated in px. 

  	optional Show.StrokeField.JoinType jointype = 3 [ default = MITER] ; // Join type

	optional Fill fill = 4;// fill value for stroke
		
	optional Show.StrokeField.CapType captype = 5;// cap type

	optional Marker headend = 6; // marker at the head end
	optional Marker tailend = 7; // marker at tail end.

	optional Show.ShapeField.BlendMode blend = 8;

	optional Show.StrokeField.StrokePosition position = 9;
	
	// Stroke Dash pattern - specifies the widths of the dashes, alternating between the painted and unpainted segments of the line
	repeated float dashPattern = 10; // deprecated - use DashGapPattern instead.
	
	optional Show.CommonField.UseParentProps useParentProps = 11;

	message StrokeGrid{

		enum StrokeGridType{
			UKNOWN_STROKE_GRID_TYPE = 0;
			DOUBLE = 1;
		}
		optional StrokeGridType strokeGridType = 1;

		optional float gap = 2;

	}
	optional StrokeGrid strokeGrid = 12;

	optional com.zoho.common.ElementCategory category = 13;

	optional float miterLimit = 14;

	enum DashPatternType {
		DEFAULT_TYPE = 0; 
		PRESERVE_DASH_GAP = 1; // Preserve Dash and gap length
		ALIGN_CORNERS_ADJUST_LENGTH = 2; // aligns dashes to corners and path ends, adjusting lengths to fit 
	}

	optional DashPatternType dashPatternType = 15;

	message DashGapPattern {
		optional float dash = 1;
		optional float gap = 2;
		optional bool isEnabled = 3;
	}
	repeated DashGapPattern dashGaps = 16;

	message StrokeSide {
		enum StrokeSideType {
			DEFAULT_STROKE_SIDE_TYPE = 0;
			ALL_SIDE = 1;
			SPECIFIC_SIDE = 2;
		}

		optional StrokeSideType side = 1;

		message SpecificSide {
			optional bool isleftEnabled = 1;
			optional bool isTopEnabled = 2;
			optional bool isRightEnabled = 3;
			optional bool isBottomEnabled = 4;

			// if the below values are not set, it will follow the stroke's given width
			optional float leftWidth = 5;
			optional float topWidth = 6;
			optional float rightWidth = 7;
			optional float bottomWidth = 8;
		}

		optional SpecificSide specificSide = 2;
  	}
	optional StrokeSide strokeSides = 17; // applicable only to rectanlges.
}
