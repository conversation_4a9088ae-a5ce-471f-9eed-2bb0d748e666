{"name": "anymatch", "version": "3.1.3", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "files": ["index.js", "index.d.ts"], "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "license": "ISC", "homepage": "https://github.com/micromatch/anymatch", "repository": {"type": "git", "url": "https://github.com/micromatch/anymatch"}, "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "devDependencies": {"mocha": "^6.1.3", "nyc": "^14.0.0"}, "engines": {"node": ">= 8"}}