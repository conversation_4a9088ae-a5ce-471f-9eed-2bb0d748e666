const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        
        // Allow any chrome-extension origin
        if (origin.startsWith('chrome-extension://')) {
            return callback(null, true);
        }
        
        // Allow specific domains
        const allowedOrigins = [
            'https://app.localvanihq.net',
            'https://app.vanihq.net',
            'http://localhost',
            'http://127.0.0.1',
            'file://'
        ];
        
        const isAllowed = allowedOrigins.some(allowed => origin.startsWith(allowed));
        callback(null, isAllowed);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}));
app.use(express.json());

// Mock template data for different prompt types
// Helper function to generate UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16).toUpperCase();
    });
}

// Helper function to convert hex color to HSB
function hexToHSB(hex) {
    // Remove # if present
    hex = hex.replace('#', '');

    // Convert to RGB
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const diff = max - min;

    let h = 0;
    let s = max === 0 ? 0 : diff / max;
    let brightness = max;

    if (diff !== 0) {
        switch (max) {
            case r: h = (g - b) / diff + (g < b ? 6 : 0); break;
            case g: h = (b - r) / diff + 2; break;
            case b: h = (r - g) / diff + 4; break;
        }
        h /= 6;
    }

    return {
        hue: h,
        saturation: s,
        brightness: brightness
    };
}

// Template configurations for different prompt types
const templateConfigs = {
    birthday: {
        backgroundColor: "#FDBE33",
        textContent: "Happy Birthday!",
        fontSize: 24,
        textColor: "#000000"
    },
    space: {
        backgroundColor: "#1a1a2e",
        textContent: "Space Adventure",
        fontSize: 28,
        textColor: "#ffffff"
    },
    celebration: {
        backgroundColor: "#ff6b6b",
        textContent: "Congratulations!",
        fontSize: 26,
        textColor: "#ffffff"
    },
    nature: {
        backgroundColor: "#4ecdc4",
        textContent: "Nature's Beauty",
        fontSize: 24,
        textColor: "#2c3e50"
    },
    default: {
        backgroundColor: "#f8f9fa",
        textContent: "Custom Template",
        fontSize: 20,
        textColor: "#333333"
    }
};

// Helper function to determine template type from prompt
function getTemplateType(prompt) {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('birthday') || lowerPrompt.includes('bday')) {
        return 'birthday';
    } else if (lowerPrompt.includes('space') || lowerPrompt.includes('rocket') || lowerPrompt.includes('astronaut')) {
        return 'space';
    } else if (lowerPrompt.includes('celebration') || lowerPrompt.includes('congratulations') || lowerPrompt.includes('party')) {
        return 'celebration';
    } else if (lowerPrompt.includes('nature') || lowerPrompt.includes('tree') || lowerPrompt.includes('flower')) {
        return 'nature';
    } else {
        return 'default';
    }
}

// Function to generate Frame proto structure
function generateFrameProto(templateType, prompt) {
    const config = templateConfigs[templateType];
    const frameId = generateUUID();
    const shapeId = generateUUID();
    const paraId = generateUUID();

    // Extract meaningful text from prompt
    const words = prompt.split(' ');
    const meaningfulWords = words.filter(word =>
        word.length > 3 &&
        !['with', 'that', 'this', 'have', 'will', 'make', 'create'].includes(word.toLowerCase())
    );

    // Determine text content
    let textContent = config.textContent;
    if (meaningfulWords.length > 0 && meaningfulWords.length <= 3) {
        textContent = meaningfulWords.join(' ');
    }

    // Convert colors to HSB
    const backgroundHSB = hexToHSB(config.backgroundColor);
    const textColorHSB = hexToHSB(config.textColor);

    return {
        id: frameId,
        meta: {
            name: "A4"
        },
        props: {
            transform: {
                dim: {
                    width: 595,
                    height: 842
                },
                pos: {
                    left: 966.5,
                    top: 15
                },
                rotAngle: 0
            },
            geom: {
                type: "PRESET",
                preset: {
                    type: "RECT"
                }
            },
            fills: [
                {
                    type: "SOLID",
                    solid: {
                        color: {
                            type: "CUSTOM",
                            colorModelRep: "HSB",
                            hsb: backgroundHSB
                        }
                    }
                }
            ],
            strokes: [
                {
                    width: 1.5,
                    fill: {
                        type: "SOLID",
                        solid: {
                            color: {
                                type: "CUSTOM",
                                tweaks: {
                                    alpha: 0
                                },
                                colorModelRep: "HSB",
                                hsb: {
                                    hue: 0,
                                    saturation: 0,
                                    brightness: 0.8399999737739563
                                }
                            }
                        }
                    },
                    captype: "FLAT"
                }
            ]
        },
        shapes: [
            {
                type: "SHAPE",
                shape: {
                    nvOProps: {
                        nvDProps: {
                            id: shapeId
                        },
                        nvODProps: {
                            textbox: false
                        },
                        nvProps: {
                            placeHolder: {
                                type: "TXT",
                                relId: "txtPlaceHolder",
                                text: "Double click to edit"
                            }
                        }
                    },
                    props: {
                        transform: {
                            rotate: 0,
                            fliph: false,
                            flipv: false,
                            dim: {
                                width: 577,
                                height: 824
                            },
                            pos: {
                                left: 9,
                                top: 9,
                                zindex: 10000
                            },
                            rotAngle: 0
                        },
                        geom: {
                            type: "PRESET",
                            preset: {
                                type: "RECT"
                            }
                        }
                    },
                    textBody: {
                        props: {
                            valign: "TOP",
                            inset: {
                                left: 10,
                                top: 5,
                                right: 10,
                                bottom: 5
                            },
                            column: {
                                num: 1,
                                gap: 0
                            },
                            autoFit: {
                                type: "NORMAL",
                                normal: {
                                    fontScale: 1,
                                    lineSpaceScale: 1
                                }
                            }
                        },
                        paras: [
                            {
                                portions: [
                                    {
                                        t: textContent,
                                        props: {
                                            font: {
                                                fontFamily: {
                                                    name: "Open Sans"
                                                }
                                            },
                                            fill: {
                                                type: "SOLID",
                                                solid: {
                                                    color: {
                                                        type: "CUSTOM",
                                                        colorModelRep: "HSB",
                                                        hsb: textColorHSB
                                                    }
                                                }
                                            },
                                            size: config.fontSize,
                                            fontweight: "NORMAL"
                                        }
                                    }
                                ],
                                style: {
                                    halign: "LEFT",
                                    level: 1,
                                    listStyle: {
                                        bullet: {
                                            type: "NONE"
                                        }
                                    },
                                    spacing: {
                                        line: {
                                            type: "PERCENT",
                                            percent: 1.2000000476837158
                                        },
                                        before: {
                                            type: "ABSOLUTE",
                                            absolute: 0
                                        }
                                    },
                                    margin: {
                                        left: 0
                                    },
                                    indent: 0,
                                    dir: "LTR",
                                    defPrProps: {
                                        font: {
                                            fontFamily: {
                                                name: "Open Sans"
                                            }
                                        },
                                        fill: {
                                            type: "SOLID",
                                            solid: {
                                                color: {
                                                    type: "CUSTOM",
                                                    colorModelRep: "HSB",
                                                    hsb: textColorHSB
                                                }
                                            }
                                        },
                                        size: config.fontSize
                                    }
                                },
                                id: paraId
                            }
                        ]
                    },
                    edit: {
                        htmlbox: true
                    }
                }
            }
        ],
        nvOProps: {}
    };
}

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'Template Generator Backend API',
        version: '1.0.0',
        endpoints: {
            'POST /generate-template': 'Generate a template from a prompt'
        }
    });
});

app.post('/generate-template', (req, res) => {
    try {
        const { prompt } = req.body;
        
        // Validate input
        if (!prompt || typeof prompt !== 'string') {
            return res.status(400).json({
                error: 'Invalid input',
                message: 'Prompt is required and must be a string'
            });
        }
        
        if (prompt.trim().length === 0) {
            return res.status(400).json({
                error: 'Invalid input',
                message: 'Prompt cannot be empty'
            });
        }
        
        console.log(`Generating template for prompt: "${prompt}"`);
        
        // Determine template type
        const templateType = getTemplateType(prompt);
        console.log(`Selected template type: ${templateType}`);

        // Generate Frame proto structure
        const frameProto = generateFrameProto(templateType, prompt);

        // Add metadata to the response
        const response = {
            ...frameProto,
            metadata: {
                prompt: prompt,
                templateType: templateType,
                generatedAt: new Date().toISOString(),
                version: '1.0.0'
            }
        };
        
        // Simulate some processing time
        setTimeout(() => {
            res.json(response);
        }, 500 + Math.random() * 1000); // 0.5-1.5 second delay
        
    } catch (error) {
        console.error('Error generating template:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to generate template'
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: 'Something went wrong'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.method} ${req.path} not found`
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Template Generator Backend running on http://localhost:${PORT}`);
    console.log(`📝 API Documentation:`);
    console.log(`   GET  /              - API information`);
    console.log(`   POST /generate-template - Generate template from prompt`);
    console.log(`   GET  /health        - Health check`);
    console.log(`\n💡 Example request:`);
    console.log(`   curl -X POST http://localhost:${PORT}/generate-template \\`);
    console.log(`        -H "Content-Type: application/json" \\`);
    console.log(`        -d '{"prompt":"Create a birthday card with space theme"}'`);
});
