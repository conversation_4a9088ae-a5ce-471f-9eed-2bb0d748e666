const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        
        // Allow any chrome-extension origin
        if (origin.startsWith('chrome-extension://')) {
            return callback(null, true);
        }
        
        // Allow specific domains
        const allowedOrigins = [
            'https://app.localvanihq.net',
            'https://app.vanihq.net',
            'http://localhost',
            'http://127.0.0.1',
            'file://'
        ];
        
        const isAllowed = allowedOrigins.some(allowed => origin.startsWith(allowed));
        callback(null, isAllowed);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}));
app.use(express.json());

// Mock template data for different prompt types
const mockTemplates = {
    birthday: {
        background: "#FDBE33",
        elements: [
            { type: "text", text: "Happy Birthday!", x: 100, y: 50, fontSize: 24 },
            { type: "image", theme: "space_rocket", x: 200, y: 150 }
        ]
    },
    space: {
        background: "#1a1a2e",
        elements: [
            { type: "text", text: "Space Adventure", x: 50, y: 30, fontSize: 28, color: "#ffffff" },
            { type: "image", theme: "space_rocket", x: 150, y: 100 },
            { type: "image", theme: "stars", x: 300, y: 80 }
        ]
    },
    celebration: {
        background: "#ff6b6b",
        elements: [
            { type: "text", text: "Congratulations!", x: 80, y: 40, fontSize: 26, color: "#ffffff" },
            { type: "image", theme: "confetti", x: 180, y: 120 },
            { type: "text", text: "Well Done!", x: 120, y: 200, fontSize: 18, color: "#ffffff" }
        ]
    },
    nature: {
        background: "#4ecdc4",
        elements: [
            { type: "text", text: "Nature's Beauty", x: 90, y: 45, fontSize: 24, color: "#2c3e50" },
            { type: "image", theme: "tree", x: 160, y: 110 },
            { type: "image", theme: "flowers", x: 250, y: 140 }
        ]
    },
    default: {
        background: "#f8f9fa",
        elements: [
            { type: "text", text: "Custom Template", x: 100, y: 50, fontSize: 20, color: "#333333" },
            { type: "image", theme: "placeholder", x: 200, y: 120 }
        ]
    }
};

// Helper function to determine template type from prompt
function getTemplateType(prompt) {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('birthday') || lowerPrompt.includes('bday')) {
        return 'birthday';
    } else if (lowerPrompt.includes('space') || lowerPrompt.includes('rocket') || lowerPrompt.includes('astronaut')) {
        return 'space';
    } else if (lowerPrompt.includes('celebration') || lowerPrompt.includes('congratulations') || lowerPrompt.includes('party')) {
        return 'celebration';
    } else if (lowerPrompt.includes('nature') || lowerPrompt.includes('tree') || lowerPrompt.includes('flower')) {
        return 'nature';
    } else {
        return 'default';
    }
}

// Helper function to customize template based on prompt
function customizeTemplate(template, prompt) {
    const customized = JSON.parse(JSON.stringify(template)); // Deep clone
    
    // Extract potential text from prompt
    const words = prompt.split(' ');
    const meaningfulWords = words.filter(word => 
        word.length > 3 && 
        !['with', 'that', 'this', 'have', 'will', 'make', 'create'].includes(word.toLowerCase())
    );
    
    // If we have meaningful words, update the main text element
    if (meaningfulWords.length > 0 && customized.elements.length > 0) {
        const textElement = customized.elements.find(el => el.type === 'text');
        if (textElement && meaningfulWords.length <= 3) {
            textElement.text = meaningfulWords.join(' ');
        }
    }
    
    return customized;
}

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'Template Generator Backend API',
        version: '1.0.0',
        endpoints: {
            'POST /generate-template': 'Generate a template from a prompt'
        }
    });
});

app.post('/generate-template', (req, res) => {
    try {
        const { prompt } = req.body;
        
        // Validate input
        if (!prompt || typeof prompt !== 'string') {
            return res.status(400).json({
                error: 'Invalid input',
                message: 'Prompt is required and must be a string'
            });
        }
        
        if (prompt.trim().length === 0) {
            return res.status(400).json({
                error: 'Invalid input',
                message: 'Prompt cannot be empty'
            });
        }
        
        console.log(`Generating template for prompt: "${prompt}"`);
        
        // Determine template type
        const templateType = getTemplateType(prompt);
        console.log(`Selected template type: ${templateType}`);
        
        // Get base template
        const baseTemplate = mockTemplates[templateType];
        
        // Customize template based on prompt
        const customizedTemplate = customizeTemplate(baseTemplate, prompt);
        
        // Add metadata
        const response = {
            ...customizedTemplate,
            metadata: {
                prompt: prompt,
                templateType: templateType,
                generatedAt: new Date().toISOString(),
                version: '1.0.0'
            }
        };
        
        // Simulate some processing time
        setTimeout(() => {
            res.json(response);
        }, 500 + Math.random() * 1000); // 0.5-1.5 second delay
        
    } catch (error) {
        console.error('Error generating template:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to generate template'
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: 'Something went wrong'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.method} ${req.path} not found`
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Template Generator Backend running on http://localhost:${PORT}`);
    console.log(`📝 API Documentation:`);
    console.log(`   GET  /              - API information`);
    console.log(`   POST /generate-template - Generate template from prompt`);
    console.log(`   GET  /health        - Health check`);
    console.log(`\n💡 Example request:`);
    console.log(`   curl -X POST http://localhost:${PORT}/generate-template \\`);
    console.log(`        -H "Content-Type: application/json" \\`);
    console.log(`        -d '{"prompt":"Create a birthday card with space theme"}'`);
});
