// Content script for template injection
// Works on: app.localvanihq.net, app.vanihq.net, local files, and localhost
// This script listens for messages from the Chrome Extension popup
// and handles template injection into the web application

console.log('Template Generator Content Script loaded on:', window.location.href);

// Listen for messages from the Chrome Extension popup
window.addEventListener('message', function(event) {
    // Verify the origin for security (optional but recommended)
    // if (event.origin !== 'chrome-extension://...') return;
    
    console.log('Content script received message:', event.data);
    
    // Check if this is our template insertion message
    if (event.data && event.data.type === 'INSERT_TEMPLATE') {
        const templateData = event.data.payload;

        console.log('Inserting template:', templateData);

        // Show a visual confirmation
        showInsertionNotification(templateData);

        // Try to inject the template into the application
        injectTemplate(templateData);
    }

    // Check if this is our RBUtils insertion message
    if (event.data && event.data.type === 'RBUTILS_INSERT_TEMPLATE') {
        const frameObj = event.data.frameObj;

        console.log('🎯 Received RBUtils insertion request:', frameObj);

        // Try to inject using RBUtils
        injectWithRBUtils(frameObj);
    }
});

/**
 * Show a visual notification that the template was received
 */
function showInsertionNotification(templateData) {
    // Create a temporary notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    
    notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">✅ Template Received!</div>
        <div style="font-size: 12px; opacity: 0.9;">
            Background: ${templateData.background || 'N/A'}<br>
            Elements: ${templateData.elements ? templateData.elements.length : 0}
        </div>
    `;
    
    // Add animation keyframes
    if (!document.getElementById('template-generator-styles')) {
        const style = document.createElement('style');
        style.id = 'template-generator-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * Inject template using RBUtils
 */
function injectWithRBUtils(frameObj) {
    try {
        console.log('🔍 Checking for RBUtils in page context...');
        console.log('🔧 RBUtils type:', typeof RBUtils);
        console.log('📦 RBUtils object:', RBUtils);

        // Create modifiedFrameObj as specified
        const modifiedFrameObj = {
            type: "FRAME",
            frame: {
                props: frameObj.props,
                shapes: frameObj.shapes,
                basekit: frameObj.basekit,
                frameIds: frameObj.frameIds,
                reactions: frameObj.reactions,
                nvOProps: {
                    nvDProps: {
                        id: frameObj.id,
                        name: frameObj.meta.name
                    }
                }
            }
        };

        console.log('📋 Modified frame object:', modifiedFrameObj);

        // Execute the insertion
        if (typeof RBUtils !== 'undefined' && RBUtils.insertion && RBUtils.insertion.addShapesInBoard) {
            console.log('✅ Calling RBUtils.insertion.addShapesInBoard...');
            RBUtils.insertion.addShapesInBoard([modifiedFrameObj]);
            console.log('🎉 Template inserted successfully using RBUtils!');

            // Show success notification
            showRBUtilsNotification('Template inserted successfully!', 'success');
        } else {
            console.error('❌ RBUtils.insertion.addShapesInBoard not available');
            console.log('Available RBUtils methods:', RBUtils && RBUtils.insertion ? Object.keys(RBUtils.insertion) : 'RBUtils not found');

            // Show error notification
            showRBUtilsNotification('RBUtils.insertion.addShapesInBoard not available', 'error');
        }
    } catch (error) {
        console.error('💥 Error in RBUtils insertion:', error);
        showRBUtilsNotification('Error: ' + error.message, 'error');
    }
}

/**
 * Show RBUtils-specific notification
 */
function showRBUtilsNotification(message, type = 'success') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? '#28a745' : '#dc3545';
    const icon = type === 'success' ? '✅' : '❌';

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;

    notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">${icon} RBUtils Insertion</div>
        <div style="font-size: 12px; opacity: 0.9;">${message}</div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * Inject the template into the web application
 * This is where you would integrate with the actual app's API
 */
function injectTemplate(templateData) {
    try {
        // Method 1: Try to call the app's global function if it exists
        if (window.app && typeof window.app.insertTemplate === 'function') {
            console.log('Calling window.app.insertTemplate()');
            window.app.insertTemplate(templateData);
            return;
        }
        
        // Method 2: Try alternative global functions
        if (window.insertTemplate && typeof window.insertTemplate === 'function') {
            console.log('Calling window.insertTemplate()');
            window.insertTemplate(templateData);
            return;
        }
        
        // Method 3: Dispatch a custom event that the app can listen for
        console.log('Dispatching custom event: templateInsert');
        const customEvent = new CustomEvent('templateInsert', {
            detail: templateData,
            bubbles: true
        });
        document.dispatchEvent(customEvent);
        
        // Method 4: Try to find and interact with specific DOM elements
        // This is a placeholder - you would customize this based on the actual app structure
        const targetElement = document.querySelector('[data-template-container]') || 
                             document.querySelector('#template-container') ||
                             document.querySelector('.template-container');
        
        if (targetElement) {
            console.log('Found target element, setting template data');
            // Store the template data as a data attribute or property
            targetElement.setAttribute('data-template', JSON.stringify(templateData));
            
            // Trigger a change event
            const changeEvent = new Event('templateChange', { bubbles: true });
            targetElement.dispatchEvent(changeEvent);
        }
        
        // Method 5: Console log for debugging (always happens)
        console.log('Template injection attempted. Template data:', templateData);
        console.log('Available methods tried:');
        console.log('- window.app.insertTemplate:', !!(window.app && window.app.insertTemplate));
        console.log('- window.insertTemplate:', !!window.insertTemplate);
        console.log('- Custom event dispatched: templateInsert');
        console.log('- Target element found:', !!targetElement);
        
    } catch (error) {
        console.error('Error injecting template:', error);
        
        // Show error notification
        showErrorNotification('Failed to inject template: ' + error.message);
    }
}

/**
 * Show an error notification
 */
function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    
    notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">❌ Error</div>
        <div style="font-size: 12px; opacity: 0.9;">${message}</div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Optional: Add a way for the web app to register its template insertion function
window.registerTemplateHandler = function(handler) {
    if (typeof handler === 'function') {
        window.app = window.app || {};
        window.app.insertTemplate = handler;
        console.log('Template handler registered successfully');
    }
};

// Let the extension know the content script is ready
console.log('Template Generator Content Script ready and listening for messages');
