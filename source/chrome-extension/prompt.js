// Frontend JavaScript for the Chrome Extension popup
// Handles user interactions, API calls, and message passing

// Configuration
const API_BASE_URL = 'http://*************:3000';

// DOM Elements
let promptInput, generateBtn, insertBtn, statusDiv, jsonOutput;
let currentTemplate = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    console.log('Template Generator popup initialized');
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    promptInput = document.getElementById('promptInput');
    generateBtn = document.getElementById('generateBtn');
    insertBtn = document.getElementById('insertBtn');
    statusDiv = document.getElementById('status');
    jsonOutput = document.getElementById('jsonOutput');
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    generateBtn.addEventListener('click', handleGenerate);
    insertBtn.addEventListener('click', handleInsert);
    
    // Enable generate button when there's text in the prompt
    promptInput.addEventListener('input', function() {
        const hasText = promptInput.value.trim().length > 0;
        generateBtn.disabled = !hasText;
    });
    
    // Allow Enter key to trigger generation
    promptInput.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' && event.ctrlKey) {
            event.preventDefault();
            if (!generateBtn.disabled) {
                handleGenerate();
            }
        }
    });
}

/**
 * Handle the Generate button click
 */
async function handleGenerate() {
    const prompt = promptInput.value.trim();
    
    if (!prompt) {
        showStatus('Please enter a prompt', 'error');
        return;
    }
    
    try {
        // Update UI to loading state
        setLoadingState(true);
        showStatus('Generating template...', 'loading');
        
        // Make API call
        const template = await generateTemplate(prompt);
        
        // Update UI with results
        currentTemplate = template;
        displayTemplate(template);
        showInsertButton(true);
        showStatus('Template generated successfully!', 'success');
        
    } catch (error) {
        console.error('Generation failed:', error);
        showStatus(`Failed to generate template: ${error.message}`, 'error');
        showInsertButton(false);
        currentTemplate = null;
    } finally {
        setLoadingState(false);
    }
}

/**
 * Handle the Insert button click
 */
async function handleInsert() {
    if (!currentTemplate) {
        showStatus('No template to insert', 'error');
        return;
    }
    
    try {
        // Check if we're on the correct domain
        const isOnTargetSite = await checkTargetSite();
        
        if (!isOnTargetSite) {
            showStatus('This extension works on web pages (not Chrome internal pages)', 'error');
            return;
        }
        
        // Send message to content script
        await sendTemplateToContentScript(currentTemplate);
        showStatus('Template sent to the target site!', 'success');
        
        // Close the popup after successful insertion
        setTimeout(() => {
            window.close();
        }, 1500);
        
    } catch (error) {
        console.error('Insertion failed:', error);
        showStatus(`Failed to insert template: ${error.message}`, 'error');
    }
}

/**
 * Generate template from API
 */
async function generateTemplate(prompt) {
    console.log('Making API request to:', `${API_BASE_URL}/generate-template`);
    console.log('Request payload:', { prompt });
    
    try {
        const response = await fetch(`${API_BASE_URL}/generate-template`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ prompt }),
            mode: 'cors' // Explicitly set CORS mode
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('API response:', data);
        return data;
        
    } catch (error) {
        console.error('Fetch error details:', error);
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        throw error;
    }
}

/**
 * Display the generated template in the JSON viewer
 */
function displayTemplate(template) {
    // Display the proto structure without metadata
    const displayData = {
        id: template.id,
        meta: template.meta,
        props: template.props,
        shapes: template.shapes,
        nvOProps: template.nvOProps
    };

    jsonOutput.textContent = JSON.stringify(displayData, null, 2);
}

/**
 * Check if current tab is on the target site
 */
async function checkTargetSite() {
    return new Promise((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                const url = tabs[0].url;
                
                // Allow on most websites for development
                // Exclude only Chrome internal pages and extensions
                const isTargetSite = url && (
                    !url.startsWith('chrome://') &&
                    !url.startsWith('chrome-extension://') &&
                    !url.startsWith('moz-extension://') &&
                    !url.startsWith('edge-extension://') &&
                    url !== 'about:blank'
                );
                
                console.log('Current URL:', url);
                console.log('Is target site:', isTargetSite);
                resolve(isTargetSite);
            } else {
                resolve(false);
            }
        });
    });
}

/**
 * Send template to content script
 */
async function sendTemplateToContentScript(template) {
    return new Promise((resolve, reject) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (!tabs[0]) {
                reject(new Error('No active tab found'));
                return;
            }

            const tabId = tabs[0].id;

            // Execute script to insert template using RBUtils
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (frameObj) => {
                    return new Promise((resolve, reject) => {
                        // Function to attempt insertion
                        const attemptInsertion = (retryCount = 0) => {
                            try {
                                // Log to page console (visible in DevTools)
                                window.console.log(`🔍 Attempt ${retryCount + 1}: Checking for RBUtils...`);

                                // Store reference to RBUtils to avoid scoping issues
                                const rbUtils = window.RBUtils;
                                window.console.log('🔧 RBUtils type:', typeof rbUtils);
                                window.console.log('📦 RBUtils object:', rbUtils);

                                // Create the modified frame object as specified
                                const modifiedFrameObj = {
                                    type: "FRAME",
                                    frame: {
                                        props: frameObj.props,
                                        shapes: frameObj.shapes,
                                        basekit: frameObj.basekit,
                                        frameIds: frameObj.frameIds,
                                        reactions: frameObj.reactions,
                                        nvOProps: {
                                            nvDProps: {
                                                id: frameObj.id,
                                                name: frameObj.meta.name
                                            }
                                        }
                                    }
                                };

                                // Check for RBUtils using the stored reference
                                if (rbUtils && rbUtils.insertion && typeof rbUtils.insertion.addShapesInBoard === 'function') {
                                    window.console.log('✅ RBUtils found, inserting template...');
                                    window.console.log('📋 Modified frame object:', modifiedFrameObj);
                                    rbUtils.insertion.addShapesInBoard([modifiedFrameObj]);
                                    window.console.log('🎉 Template inserted successfully using RBUtils');
                                    resolve('Template inserted successfully');
                                } else {
                                    // Log what's actually available
                                    window.console.log('❌ RBUtils check failed:');
                                    window.console.log('  - RBUtils exists:', typeof rbUtils !== 'undefined');
                                    window.console.log('  - RBUtils.insertion exists:', rbUtils && !!rbUtils.insertion);
                                    window.console.log('  - addShapesInBoard exists:', rbUtils && rbUtils.insertion && !!rbUtils.insertion.addShapesInBoard);
                                    window.console.log('  - addShapesInBoard type:', rbUtils && rbUtils.insertion && typeof rbUtils.insertion.addShapesInBoard);

                                    if (rbUtils && rbUtils.insertion) {
                                        window.console.log('🔍 Available methods in RBUtils.insertion:', Object.keys(rbUtils.insertion));
                                    }

                                    // Retry up to 5 times with 1 second delay
                                    if (retryCount < 5) {
                                        window.console.log(`⏳ RBUtils not ready, retrying in 1 second... (attempt ${retryCount + 1}/5)`);
                                        setTimeout(() => attemptInsertion(retryCount + 1), 1000);
                                    } else {
                                        window.console.error('💥 RBUtils.insertion.addShapesInBoard not available after 5 attempts');
                                        window.console.log('🌐 Available global objects:', Object.keys(window).filter(key => key.includes('RB') || key.includes('Utils')));
                                        reject(new Error('RBUtils not available on this page after multiple attempts'));
                                    }
                                }
                            } catch (error) {
                                window.console.error('💥 Error inserting template:', error);
                                reject(error);
                            }
                        };

                        // Start the insertion attempt
                        attemptInsertion();
                    });
                },
                args: [template]
            }, (results) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(results);
                }
            });
        });
    });
}

/**
 * Show status message
 */
function showStatus(message, type = 'info') {
    statusDiv.className = `status ${type}`;
    
    if (type === 'loading') {
        statusDiv.innerHTML = `<span class="spinner"></span>${message}`;
    } else {
        statusDiv.textContent = message;
    }
    
    statusDiv.classList.remove('hidden');
    
    // Auto-hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.classList.add('hidden');
        }, 3000);
    }
}

/**
 * Set loading state for UI
 */
function setLoadingState(isLoading) {
    generateBtn.disabled = isLoading;
    promptInput.disabled = isLoading;
    
    if (isLoading) {
        generateBtn.innerHTML = '<span class="spinner"></span>Generating...';
    } else {
        generateBtn.textContent = 'Generate Template';
    }
}

/**
 * Show or hide the insert button
 */
function showInsertButton(show) {
    if (show) {
        insertBtn.classList.remove('hidden');
        insertBtn.disabled = false;
    } else {
        insertBtn.classList.add('hidden');
        insertBtn.disabled = true;
    }
}

/**
 * Utility function to format JSON for display
 */
function formatJSON(obj) {
    return JSON.stringify(obj, null, 2);
}

// Error handling for uncaught errors
window.addEventListener('error', function(event) {
    console.error('Uncaught error in popup:', event.error);
    showStatus('An unexpected error occurred', 'error');
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection in popup:', event.reason);
    showStatus('An unexpected error occurred', 'error');
});
