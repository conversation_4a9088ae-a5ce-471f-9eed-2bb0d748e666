// Frontend JavaScript for the Chrome Extension popup
// Handles user interactions, API calls, and message passing

// Configuration
const API_BASE_URL = 'http://*************:3000';

// DOM Elements
let promptInput, generateBtn, insertBtn, statusDiv, jsonOutput;
let currentTemplate = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    promptInput = document.getElementById('promptInput');
    generateBtn = document.getElementById('generateBtn');
    insertBtn = document.getElementById('insertBtn');
    statusDiv = document.getElementById('status');
    jsonOutput = document.getElementById('jsonOutput');
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    generateBtn.addEventListener('click', handleGenerate);
    insertBtn.addEventListener('click', handleInsert);
    
    // Enable generate button when there's text in the prompt
    promptInput.addEventListener('input', function() {
        const hasText = promptInput.value.trim().length > 0;
        generateBtn.disabled = !hasText;
    });
    
    // Allow Enter key to trigger generation
    promptInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !generateBtn.disabled) {
            handleGenerate();
        }
    });
}

/**
 * Handle the Generate button click
 */
async function handleGenerate() {
    const prompt = promptInput.value.trim();
    
    if (!prompt) {
        showStatus('Please enter a prompt', 'error');
        return;
    }
    
    try {
        setLoadingState(true);
        showStatus('Generating template...', 'loading');
        showInsertButton(false);
        
        // Clear previous results
        jsonOutput.textContent = '';
        
        // Make API call
        const template = await generateTemplate(prompt);
        
        // Update UI with results
        currentTemplate = template;
        displayTemplate(template);
        showInsertButton(true);
        showStatus('Template generated successfully!', 'success');
        
    } catch (error) {
        console.error('Generation failed:', error);
        showStatus(`Failed to generate template: ${error.message}`, 'error');
        showInsertButton(false);
        currentTemplate = null;
    } finally {
        setLoadingState(false);
    }
}

/**
 * Handle the Insert button click
 */
async function handleInsert() {
    if (!currentTemplate) {
        showStatus('No template to insert', 'error');
        return;
    }
    
    try {
        // Check if we're on the correct domain
        const isValidDomain = await checkCurrentDomain();
        if (!isValidDomain) {
            showStatus('Please navigate to a supported website first', 'error');
            return;
        }
        
        // Send message to content script
        await sendTemplateToContentScript(currentTemplate);
        showStatus('Template sent to the target site!', 'success');
        
        // Close the popup after successful insertion
        setTimeout(() => {
            window.close();
        }, 1500);
        
    } catch (error) {
        console.error('Insertion failed:', error);
        showStatus(`Failed to insert template: ${error.message}`, 'error');
    }
}

/**
 * Generate template from API
 */
async function generateTemplate(prompt) {
    console.log('Making API request to:', `${API_BASE_URL}/generate-template`);
    console.log('Request payload:', { prompt });
    
    try {
        const response = await fetch(`${API_BASE_URL}/generate-template`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ prompt }),
            mode: 'cors' // Explicitly set CORS mode
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('API response:', data);
        return data;
        
    } catch (error) {
        console.error('Fetch error details:', error);
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        throw error;
    }
}

/**
 * Display the generated template in the JSON viewer
 */
function displayTemplate(template) {
    // Display the proto structure without metadata
    const displayData = {
        id: template.id,
        meta: template.meta,
        props: template.props,
        shapes: template.shapes,
        nvOProps: template.nvOProps
    };
    
    jsonOutput.textContent = JSON.stringify(displayData, null, 2);
}

/**
 * Check if current tab is on the target site
 */
async function checkCurrentDomain() {
    return new Promise((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (!tabs[0]) {
                resolve(false);
                return;
            }
            
            const url = tabs[0].url;
            const validDomains = [
                'app.localvanihq.net',
                'app.vanihq.net',
                'show.zoho.com',
                'localhost',
                'file://'
            ];
            
            const isValid = validDomains.some(domain => url.includes(domain));
            resolve(isValid);
        });
    });
}

/**
 * Send template to content script
 */
async function sendTemplateToContentScript(template) {
    return new Promise((resolve, reject) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (!tabs[0]) {
                reject(new Error('No active tab found'));
                return;
            }
            
            const tabId = tabs[0].id;
            
            // Create clean frameObj without metadata in correct order
            const cleanFrameObj = {
                id: template.id,
                meta: template.meta,
                props: template.props,
                shapes: template.shapes,
                nvOProps: template.nvOProps
            };

            // Send message to content script to handle RBUtils insertion
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (frameObj) => {
                    // Send a message to the content script using postMessage
                    window.postMessage({
                        type: 'RBUTILS_INSERT_TEMPLATE',
                        frameObj: frameObj
                    }, '*');

                    console.log('📤 Sent RBUTILS_INSERT_TEMPLATE message to content script');
                    return 'Message sent to content script';
                },
                args: [cleanFrameObj]
            }, (results) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(results);
                }
            });
        });
    });
}

/**
 * Show status message
 */
function showStatus(message, type = 'info') {
    statusDiv.className = `status ${type}`;
    
    if (type === 'loading') {
        statusDiv.innerHTML = `<span class="spinner"></span>${message}`;
    } else {
        statusDiv.textContent = message;
    }
    
    statusDiv.classList.remove('hidden');
    
    // Auto-hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.classList.add('hidden');
        }, 3000);
    }
}

/**
 * Set loading state for the generate button
 */
function setLoadingState(isLoading) {
    if (isLoading) {
        generateBtn.disabled = true;
        generateBtn.textContent = 'Generating...';
    } else {
        generateBtn.disabled = promptInput.value.trim().length === 0;
        generateBtn.textContent = 'Generate Template';
    }
}

/**
 * Show or hide the insert button
 */
function showInsertButton(show) {
    if (show) {
        insertBtn.classList.remove('hidden');
        insertBtn.disabled = false;
    } else {
        insertBtn.classList.add('hidden');
        insertBtn.disabled = true;
    }
}

/**
 * Utility function to format JSON for display
 */
function formatJSON(obj) {
    return JSON.stringify(obj, null, 2);
}
