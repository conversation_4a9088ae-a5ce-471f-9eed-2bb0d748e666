# Template Generation Tool

A Chrome Extension tool for the design team to generate templates and inject them into the web application.

## 🚀 Quick Start

### 1. Backend Setup
```bash
cd source/backend
npm install
npm run dev
```
The server will start on http://localhost:3000
**⚠️ Important: Keep the backend server running for the extension to work properly**

### 2. Chrome Extension Setup
1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked" and select the `source/chrome-extension` folder
4. The extension icon will appear in your toolbar
5. **For local file testing**: Click on "Details" for the Template Generator extension, then enable "Allow access to file URLs"
6. Click the extension icon to open the popup interface

### 3. Testing
1. Open any website or the test page: `source/test-page.html` in your browser
2. Click "Register Template Handler" on the test page (if using test page)
3. Click the extension icon to open the popup
4. Enter a prompt and click "Generate"
5. Click "Insert" to test the injection (works on most websites for development)

## 📁 Project Structure

```
source/                      # All project source files
├── chrome-extension/          # Chrome Extension files
│   ├── manifest.json         # Extension manifest (Manifest V3)
│   ├── prompt.html          # Main UI popup page
│   ├── prompt.js            # Frontend logic & API calls
│   └── content-script.js    # Content script for injection
├── backend/                 # Node.js backend server
│   ├── package.json        # Backend dependencies
│   └── server.js           # Express server with mock templates
└── test-page.html          # Test page for development
└── README.md               # This documentation
```

## 🔄 Usage Flow

1. **Open Extension**: Click the extension icon in Chrome toolbar (opens popup)
2. **Enter Prompt**: Type a description (e.g., "Create a birthday card with space theme")
3. **Generate**: Click "Generate Template" to call the backend API
4. **Review**: View the generated JSON in the popup interface
5. **Navigate**: Go to any website (works on most web pages for development)
6. **Insert**: Click "Insert Template" to inject into the web app (popup will close automatically)

## 🎯 Template Types

The backend automatically detects template types based on keywords:

- **Birthday**: "birthday", "bday" → Yellow background with birthday text
- **Space**: "space", "rocket", "astronaut" → Dark background with space elements
- **Celebration**: "celebration", "congratulations", "party" → Red background with celebration elements
- **Nature**: "nature", "tree", "flower" → Teal background with nature elements
- **Default**: Any other prompt → Light background with generic elements

## 🔧 API Endpoints

### POST /generate-template
- **URL**: `http://localhost:3000/generate-template`
- **Body**: `{ "prompt": "string" }`
- **Response**:
```json
{
  "background": "#FDBE33",
  "elements": [
    { "type": "text", "text": "Happy Birthday!", "x": 100, "y": 50, "fontSize": 24 },
    { "type": "image", "theme": "space_rocket", "x": 200, "y": 150 }
  ],
  "metadata": {
    "prompt": "Create a birthday card with space theme",
    "templateType": "birthday",
    "generatedAt": "2025-07-14T06:03:17.551Z",
    "version": "1.0.0"
  }
}
```

### GET /health
- **URL**: `http://localhost:3000/health`
- **Response**: Server health status

## 🧪 Testing & Development

### Using the Test Page
1. Open `source/test-page.html` in your browser
2. Click "Register Template Handler" to simulate app.localvanihq.net or app.vanihq.net
3. Test the extension popup and template injection

### Manual API Testing
```bash
# Test the API directly
curl -X POST http://localhost:3000/generate-template \
     -H "Content-Type: application/json" \
     -d '{"prompt":"Create a birthday card with space theme"}'
```

### Chrome Extension Testing
1. Load the extension in Chrome (see setup above)
2. Click the extension icon to open the popup
3. Test template generation in the popup interface
4. Navigate to the test page, app.localvanihq.net, or app.vanihq.net
5. Test template insertion

## 🔌 Integration with app.localvanihq.net and app.vanihq.net

The content script provides multiple integration methods:

### Method 1: Global Function (Recommended)
```javascript
// In your web app, register a handler:
window.app = window.app || {};
window.app.insertTemplate = function(templateData) {
    // Handle the template insertion
    console.log('Received template:', templateData);
    // Your implementation here
};
```

### Method 2: Custom Event Listener
```javascript
// Listen for the custom event
document.addEventListener('templateInsert', function(event) {
    const templateData = event.detail;
    // Handle the template insertion
});
```

### Method 3: Alternative Global Function
```javascript
// Alternative global function name
window.insertTemplate = function(templateData) {
    // Handle the template insertion
};
```

## 🛠️ Customization

### Adding New Template Types
Edit `source/backend/server.js` and add new entries to the `mockTemplates` object:

```javascript
const mockTemplates = {
    // ... existing templates
    newType: {
        background: "#your-color",
        elements: [
            // your elements
        ]
    }
};
```

### Modifying the UI
- Edit `source/chrome-extension/prompt.html` for layout changes
- Edit `source/chrome-extension/prompt.js` for functionality changes
- Styles are embedded in the HTML file

## 🚨 Troubleshooting

### Backend Issues
- **Server won't start**: Check if port 3000 is available
- **CORS errors**: Verify the CORS configuration in `server.js`
- **API errors**: Check the server logs in the terminal

### Extension Issues
- **Extension won't load**: Check for manifest.json syntax errors
- **Popup won't open**: Verify all files are in the source/chrome-extension folder
- **API calls fail**: Ensure the backend server is running

### Injection Issues
- **Template not inserting**: Check browser console for errors
- **No handler found**: Verify the target site has registered a handler
- **Permission denied**: Ensure the extension has permissions for the target site

## 📝 Next Steps

1. **Replace mock data**: Connect to a real AI/template generation service
2. **Enhanced UI**: Add more styling and user experience improvements
3. **Error handling**: Add more robust error handling and user feedback
4. **Template preview**: Add visual preview of templates before insertion
5. **User preferences**: Add settings for API endpoints and preferences
